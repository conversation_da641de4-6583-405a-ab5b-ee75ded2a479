"""
Lighter交易所客户端

基于官方 lighter-python SDK 的 WebSocket 实现
"""

import asyncio
import json
import time
import threading
from typing import Dict, List, Optional, Callable, Any, Tuple
from decimal import Decimal
import structlog
from dataclasses import dataclass

logger = structlog.get_logger(__name__)

# 导入Lighter SDK
try:
    import lighter
    LIGHTER_SDK_AVAILABLE = True
    logger.info("✅ Lighter SDK已成功导入")
except ImportError:
    lighter = None
    LIGHTER_SDK_AVAILABLE = False
    logger.warning("❌ Lighter SDK未安装，请安装lighter-python包")


@dataclass
class LighterOrderBook:
    """Lighter订单簿数据"""
    symbol: str
    bids: List[List[float]]  # [[price, amount], ...]
    asks: List[List[float]]  # [[price, amount], ...]
    timestamp: float


@dataclass
class LighterTrade:
    """Lighter交易数据"""
    symbol: str
    price: float
    amount: float
    side: str  # "buy" or "sell"
    timestamp: float


class LighterClient:
    """
    Lighter交易所客户端

    使用官方lighter-python SDK的WsClient获取100%真实的WebSocket数据
    增强版本：包含连接监控、心跳机制和自动重连功能
    """

    def __init__(self, symbol: str = "BTC/USDT", is_paper_trading: bool = True):
        """
        初始化Lighter客户端

        Args:
            symbol: 交易对符号
            is_paper_trading: 是否为模拟交易（仅影响下单操作）
        """
        if not LIGHTER_SDK_AVAILABLE:
            raise RuntimeError("Lighter SDK未安装，请运行: pip install git+https://github.com/elliottech/lighter-python.git")

        self.symbol = symbol
        self.is_paper_trading = is_paper_trading
        self.market_id = self._get_market_id_from_symbol(symbol)

        # 真实数据存储
        self.current_orderbook: Optional[LighterOrderBook] = None
        self.current_price: Optional[float] = None
        self.orderbook_callbacks: List[Callable] = []
        self.trade_callbacks: List[Callable] = []

        # WebSocket客户端
        self.ws_client: Optional[Any] = None
        self.ws_thread: Optional[threading.Thread] = None
        self.is_running = False

        # 🔧 新增：连接监控和重连机制
        self.last_update_time = None
        self.connection_monitor_thread: Optional[threading.Thread] = None
        self.should_monitor = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_interval = 5  # 秒
        self.heartbeat_interval = 30  # 心跳间隔（秒）
        self.connection_timeout = 60  # 连接超时时间（秒）

        # 🔧 新增：重复数据检测
        self.last_orderbook_hash = None
        self.duplicate_data_start_time = None
        self.max_duplicate_data_time = 120  # 最大重复数据时间（秒）
        self.price_unchanged_threshold = 60  # 价格不变阈值（秒）

        # 回调函数
        self.orderbook_callback = None
        self.trade_callback = None

        # 初始化状态
        self.status = {
            "exchange": "Lighter",
            "symbol": symbol,
            "market_id": self.market_id,
            "data_source": "REAL_WEBSOCKET_ONLY",
            "trading_mode": "paper" if is_paper_trading else "live",
            "connection_status": "disconnected",
            "api_url": "wss://mainnet.zklighter.elliot.ai/ws",
            "last_update": None,
            "sdk_version": "官方lighter-python",
            # 🔧 新增状态字段
            "reconnect_attempts": 0,
            "last_heartbeat": None,
            "connection_health": "unknown"
        }

        logger.info("🚀 Lighter客户端初始化完成 (增强版)", **self.status)

    def set_orderbook_callback(self, callback: Callable) -> None:
        """设置订单簿更新回调函数"""
        self.orderbook_callback = callback
        logger.debug("设置Lighter订单簿回调函数")

    def set_trade_callback(self, callback: Callable) -> None:
        """设置交易更新回调函数"""
        self.trade_callback = callback
        logger.debug("设置Lighter交易回调函数")

    def _get_market_id_from_symbol(self, symbol: str) -> int:
        """根据symbol获取market_id"""
        # 根据官方示例，使用简单的映射
        # market_id=0 对应第一个市场，market_id=1 对应第二个市场
        symbol_market_map = {
            "BTC/USDT": 1,  # BTC对应market_id=1
            "ETH/USDT": 0,  # ETH对应market_id=0
            "BTC/USD": 1,
            "ETH/USD": 0,
            "BTCUSDT": 1,
            "ETHUSDT": 0,
        }
        market_id = symbol_market_map.get(symbol, 1)  # 默认返回1 (BTC)
        logger.debug("🗺️ Symbol映射", symbol=symbol, market_id=market_id)
        return market_id

    def _get_symbol_from_market_id(self, market_id: int) -> str:
        """根据market_id获取symbol"""
        market_symbol_map = {
            0: "ETH/USDT",  # market_id=0 对应 ETH
            1: "BTC/USDT",  # market_id=1 对应 BTC
        }
        return market_symbol_map.get(market_id, "BTC/USDT")

    def on_order_book_update(self, market_id: int, order_book: dict):
        """
        处理订单簿更新回调（官方SDK格式）
        增强版本：包含更好的错误处理和数据验证
        """
        try:
            # 验证输入数据
            if not isinstance(order_book, dict):
                logger.warning("收到无效的订单簿数据格式", data_type=type(order_book))
                return

            symbol = self._get_symbol_from_market_id(market_id)

            # 🔧 调试：打印原始数据格式以了解结构
            logger.debug("📊 收到Lighter订单簿更新",
                        market_id=market_id,
                        symbol=symbol,
                        raw_data_keys=list(order_book.keys()),
                        raw_data_sample=str(order_book)[:200] + "..." if len(str(order_book)) > 200 else str(order_book))

            # 解析订单簿数据
            bids = []
            asks = []

            # 🔧 调试：检查数据结构（仅在首次接收时显示）
            if not hasattr(self, '_data_structure_logged'):
                logger.info("🔍 Lighter数据结构",
                           all_keys=list(order_book.keys()),
                           sample_bid=order_book.get('bids', [{}])[0] if order_book.get('bids') else {},
                           sample_ask=order_book.get('asks', [{}])[0] if order_book.get('asks') else {})
                self._data_structure_logged = True

            # 尝试多种可能的字段名
            possible_bid_fields = ['bids', 'buy_orders', 'buys', 'bid_orders', 'buy', 'b']
            possible_ask_fields = ['asks', 'sell_orders', 'sells', 'ask_orders', 'sell', 'a']

            # 解析买单数据
            bid_data = None
            for field in possible_bid_fields:
                if field in order_book and order_book[field]:
                    bid_data = order_book[field]
                    break

            if bid_data:
                for bid in bid_data:
                    try:
                        if isinstance(bid, dict):
                            # Lighter使用 'price' 和 'size' 字段
                            price_fields = ['price', 'p', 'px', 'rate']
                            amount_fields = ['size', 'amount', 'quantity', 'qty', 'volume']

                            price = None
                            amount = None

                            for pf in price_fields:
                                if pf in bid and bid[pf] is not None:
                                    price = float(bid[pf])
                                    break

                            for af in amount_fields:
                                if af in bid and bid[af] is not None:
                                    amount = float(bid[af])
                                    break

                            if price and amount and price > 0 and amount > 0:
                                bids.append([price, amount])

                        elif isinstance(bid, list) and len(bid) >= 2:
                            price = float(bid[0])
                            amount = float(bid[1])
                            if price > 0 and amount > 0:
                                bids.append([price, amount])

                    except (ValueError, TypeError, IndexError) as e:
                        logger.debug("解析买单数据失败", bid=str(bid)[:50], error=str(e))
                        continue

            # 解析卖单数据
            ask_data = None
            for field in possible_ask_fields:
                if field in order_book and order_book[field]:
                    ask_data = order_book[field]
                    break

            if ask_data:
                for ask in ask_data:
                    try:
                        if isinstance(ask, dict):
                            # Lighter使用 'price' 和 'size' 字段
                            price_fields = ['price', 'p', 'px', 'rate']
                            amount_fields = ['size', 'amount', 'quantity', 'qty', 'volume']

                            price = None
                            amount = None

                            for pf in price_fields:
                                if pf in ask and ask[pf] is not None:
                                    price = float(ask[pf])
                                    break

                            for af in amount_fields:
                                if af in ask and ask[af] is not None:
                                    amount = float(ask[af])
                                    break

                            if price and amount and price > 0 and amount > 0:
                                asks.append([price, amount])

                        elif isinstance(ask, list) and len(ask) >= 2:
                            price = float(ask[0])
                            amount = float(ask[1])
                            if price > 0 and amount > 0:
                                asks.append([price, amount])

                    except (ValueError, TypeError, IndexError) as e:
                        logger.debug("解析卖单数据失败", ask=str(ask)[:50], error=str(e))
                        continue

            # 验证解析结果
            if not bids or not asks:
                logger.warning("订单簿数据不完整", bids_count=len(bids), asks_count=len(asks))
                return

            # 🔧 新增：重复数据检测
            current_time = time.time()
            # 创建数据哈希用于检测重复
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            data_hash = f"{best_bid}_{best_ask}_{len(bids)}_{len(asks)}"

            if self.last_orderbook_hash == data_hash:
                # 检测到重复数据
                if self.duplicate_data_start_time is None:
                    self.duplicate_data_start_time = current_time
                    logger.debug(f"🔄 检测到Lighter重复数据开始: {data_hash}")
                else:
                    duplicate_duration = current_time - self.duplicate_data_start_time
                    if duplicate_duration > self.max_duplicate_data_time:
                        logger.error(f"🚨 Lighter重复数据超时！已持续{duplicate_duration:.1f}秒")
                        self.status["connection_health"] = "stale_data"

                        # 尝试重连
                        if self.reconnect_attempts < self.max_reconnect_attempts:
                            logger.info(f"🔄 因重复数据尝试重连Lighter WebSocket (第{self.reconnect_attempts + 1}次)")
                            self._restart_websocket_thread()
                            return
                    elif duplicate_duration > self.price_unchanged_threshold:
                        logger.debug(f"⚠️ Lighter价格未变化已{duplicate_duration:.1f}秒")
                        self.status["connection_health"] = "stale_data"
                # 即使是重复数据，也要继续处理，因为可能是正常的市场状态
            else:
                # 数据有变化，重置重复数据检测
                if self.duplicate_data_start_time is not None:
                    duration = current_time - self.duplicate_data_start_time
                    logger.debug(f"✅ Lighter数据恢复更新，重复数据持续了{duration:.1f}秒")
                self.duplicate_data_start_time = None
                self.last_orderbook_hash = data_hash

            # 创建订单簿对象
            orderbook = LighterOrderBook(
                symbol=symbol,
                bids=sorted(bids, key=lambda x: x[0], reverse=True)[:10],  # 前10档买单
                asks=sorted(asks, key=lambda x: x[0])[:10],  # 前10档卖单
                timestamp=time.time()
            )

            self.current_orderbook = orderbook

            # 更新当前价格（使用最优买卖价格的中间价）
            if bids and asks:
                best_bid = bids[0][0]
                best_ask = asks[0][0]
                self.current_price = (best_bid + best_ask) / 2

                logger.info("📊 Lighter订单簿更新",
                           symbol=symbol,
                           market_id=market_id,
                           bid=best_bid,
                           ask=best_ask,
                           mid_price=self.current_price,
                           bids_count=len(bids),
                           asks_count=len(asks))

                # 调用回调函数
                if self.orderbook_callback:
                    try:
                        self.orderbook_callback(orderbook)
                    except Exception as e:
                        logger.error("Lighter订单簿回调函数执行失败", error=str(e))

            # 🔧 更新连接状态和时间戳
            self.last_update_time = current_time
            self.status["last_update"] = current_time
            self.status["connection_status"] = "connected"
            if self.duplicate_data_start_time is None:
                self.status["connection_health"] = "healthy"

            # 触发回调
            for callback in self.orderbook_callbacks:
                try:
                    callback(orderbook)
                except Exception as e:
                    logger.error("订单簿回调错误", error=str(e))

        except Exception as e:
            logger.error("处理订单簿更新失败", error=str(e), market_id=market_id)

    def on_account_update(self, account_id: int, account: dict):
        """
        处理账户更新回调（官方SDK格式）
        """
        logger.debug("💰 账户更新", account_id=account_id, account=account)

    async def initialize(self) -> bool:
        """
        初始化Lighter客户端
        增强版本：包含更好的初始化检查和超时处理
        """
        try:
            if not LIGHTER_SDK_AVAILABLE:
                raise RuntimeError("Lighter SDK不可用")

            logger.info("🔌 正在初始化Lighter WebSocket连接...")

            # 记录初始化时间
            self._initialization_time = time.time()

            # 根据官方示例创建WsClient
            self.ws_client = lighter.WsClient(
                order_book_ids=[self.market_id],  # 订阅指定的市场ID
                account_ids=[],  # 暂时不订阅账户更新
                on_order_book_update=self.on_order_book_update,
                on_account_update=self.on_account_update,
            )

            # 在单独线程中运行WebSocket
            self.is_running = True
            self.ws_thread = threading.Thread(target=self._run_websocket, daemon=True)
            self.ws_thread.start()

            # 🔧 启动连接监控
            self._start_connection_monitor()

            # 🔧 启动健康检查任务
            self._start_health_check()

            # 等待连接建立 - 增加超时时间和更好的检查
            max_wait_time = 15  # 最大等待15秒
            check_interval = 0.5  # 每0.5秒检查一次
            waited_time = 0

            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval

                # 检查是否收到数据
                if self.current_orderbook is not None:
                    logger.info("✅ Lighter WebSocket连接成功", **self.status)
                    return True

                # 检查WebSocket线程是否还活着
                if not self.ws_thread.is_alive():
                    logger.error("❌ WebSocket线程已死亡")
                    break

                # 每5秒输出一次等待状态
                if int(waited_time) % 5 == 0 and waited_time > 0:
                    logger.info(f"⏳ 等待Lighter连接建立... ({waited_time:.1f}s/{max_wait_time}s)")

            # 超时或失败
            if self.current_orderbook is None:
                logger.warning("⚠️ Lighter WebSocket连接超时或失败")
                # 尝试一次重连
                logger.info("🔄 尝试重新连接...")
                self._restart_websocket_thread()

                # 再等待5秒
                await asyncio.sleep(5)
                if self.current_orderbook is not None:
                    logger.info("✅ Lighter WebSocket重连成功")
                    return True
                else:
                    logger.error("❌ Lighter WebSocket重连失败")
                    return False

            return False

        except Exception as e:
            logger.error("❌ Lighter初始化失败", error=str(e))
            raise

    def _run_websocket(self):
        """
        在单独线程中运行WebSocket客户端
        增强版本：包含更好的错误处理和重连逻辑
        """
        retry_count = 0
        max_retries = 5

        while self.is_running and retry_count < max_retries:
            try:
                logger.info("🚀 启动Lighter WebSocket客户端...", retry_count=retry_count)

                # 确保WebSocket客户端存在
                if not self.ws_client:
                    logger.error("❌ WebSocket客户端未初始化")
                    break

                # 运行WebSocket客户端（这是阻塞调用）
                self.ws_client.run()

                # 如果正常退出，说明连接被正常关闭
                if self.is_running:
                    logger.info("✅ WebSocket客户端正常退出")
                break

            except Exception as e:
                retry_count += 1
                logger.error("WebSocket运行错误",
                           error=str(e),
                           retry_count=retry_count,
                           max_retries=max_retries)

                if retry_count < max_retries and self.is_running:
                    # 等待一段时间后重试
                    wait_time = min(retry_count * 2, 10)  # 指数退避，最大10秒
                    logger.info(f"⏳ {wait_time}秒后重试WebSocket连接...")
                    time.sleep(wait_time)

                    # 重新创建WebSocket客户端
                    try:
                        self.ws_client = lighter.WsClient(
                            order_book_ids=[self.market_id],
                            account_ids=[],
                            on_order_book_update=self.on_order_book_update,
                            on_account_update=self.on_account_update,
                        )
                        logger.info("🔄 WebSocket客户端已重新创建")
                    except Exception as recreate_error:
                        logger.error("重新创建WebSocket客户端失败", error=str(recreate_error))
                        break
                else:
                    logger.error("❌ WebSocket重试次数已达上限或系统已停止")
                    break

        # 清理状态
        self.is_running = False
        self.status["connection_status"] = "disconnected"
        self.status["connection_health"] = "disconnected"
        logger.warning("📵 Lighter WebSocket连接已断开")

    # 🔧 新增：连接监控机制
    def _start_connection_monitor(self):
        """启动连接监控线程"""
        if self.connection_monitor_thread is None or not self.connection_monitor_thread.is_alive():
            self.should_monitor = True
            self.connection_monitor_thread = threading.Thread(target=self._monitor_connection, daemon=True)
            self.connection_monitor_thread.start()
            logger.info("🔍 Lighter连接监控已启动")

    def _start_health_check(self):
        """启动健康检查线程"""
        if not hasattr(self, 'health_check_thread') or not self.health_check_thread or not self.health_check_thread.is_alive():
            self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
            self.health_check_thread.start()
            logger.info("💓 Lighter健康检查已启动")

    def _health_check_loop(self):
        """健康检查循环"""
        last_data_check = time.time()
        health_check_interval = 30  # 每30秒检查一次

        while self.should_monitor and self.is_running:
            try:
                current_time = time.time()

                # 每30秒进行一次全面健康检查
                if current_time - last_data_check >= health_check_interval:
                    self._perform_health_check()
                    last_data_check = current_time

                time.sleep(5)  # 每5秒检查一次循环条件

            except Exception as e:
                logger.error("健康检查循环异常", error=str(e))
                time.sleep(10)

    def _perform_health_check(self):
        """执行健康检查"""
        try:
            current_time = time.time()

            # 检查数据更新频率
            if self.last_update_time:
                time_since_update = current_time - self.last_update_time

                if time_since_update > 120:  # 2分钟没有数据更新
                    logger.warning(f"💔 健康检查：数据更新异常，{time_since_update:.1f}秒未收到更新")

                    # 尝试重启WebSocket
                    if self.reconnect_attempts < self.max_reconnect_attempts:
                        logger.info("🔄 健康检查触发WebSocket重启")
                        self._restart_websocket_thread()
                    else:
                        logger.error("❌ 健康检查：重连次数已达上限")

                elif time_since_update > 60:  # 1分钟没有数据更新，发出警告
                    logger.warning(f"⚠️ 健康检查：数据更新缓慢，{time_since_update:.1f}秒未收到更新")
                else:
                    logger.debug(f"💚 健康检查：数据更新正常，{time_since_update:.1f}秒前有更新")
            else:
                # 检查初始化后是否收到过数据
                if hasattr(self, '_initialization_time'):
                    time_since_init = current_time - self._initialization_time
                    if time_since_init > 60:  # 初始化1分钟后还没收到数据
                        logger.error(f"💔 健康检查：初始化后{time_since_init:.1f}秒仍未收到数据")
                        self._restart_websocket_thread()

            # 检查WebSocket线程状态
            if not self.ws_thread or not self.ws_thread.is_alive():
                logger.warning("💔 健康检查：WebSocket线程已死亡")
                self._restart_websocket_thread()

            # 更新健康状态
            if self.status.get("connection_health") not in ["failed", "stale_data"]:
                self.status["connection_health"] = "healthy"

        except Exception as e:
            logger.error("执行健康检查失败", error=str(e))

    def _monitor_connection(self):
        """
        监控连接状态和数据更新
        增强版本：包含更智能的健康检查和恢复机制
        """
        consecutive_failures = 0
        max_consecutive_failures = 3

        while self.should_monitor and self.is_running:
            try:
                current_time = time.time()

                # 检查WebSocket线程是否还活着
                if self.ws_thread and not self.ws_thread.is_alive():
                    logger.warning("🚨 WebSocket线程已死亡，尝试重启")
                    self._restart_websocket_thread()
                    consecutive_failures += 1

                    if consecutive_failures >= max_consecutive_failures:
                        logger.error("❌ WebSocket线程连续重启失败，停止监控")
                        self.status["connection_health"] = "failed"
                        break

                    time.sleep(5)
                    continue

                # 检查数据更新时间
                if self.last_update_time:
                    time_since_update = current_time - self.last_update_time

                    if time_since_update > self.connection_timeout:
                        logger.warning(f"🚨 Lighter数据超时！{time_since_update:.1f}秒未收到更新")
                        self.status["connection_health"] = "stale"
                        consecutive_failures += 1

                        # 尝试重连
                        if self.reconnect_attempts < self.max_reconnect_attempts:
                            logger.info(f"🔄 尝试重连Lighter WebSocket (第{self.reconnect_attempts + 1}次)")
                            self._attempt_reconnect()
                        else:
                            logger.error("❌ Lighter重连次数已达上限，停止重连")
                            self.status["connection_health"] = "failed"
                            break
                    elif time_since_update > self.heartbeat_interval:
                        logger.debug(f"💓 Lighter心跳检查: {time_since_update:.1f}秒前有数据更新")
                        self.status["connection_health"] = "healthy"
                        self.status["last_heartbeat"] = current_time
                        consecutive_failures = 0  # 重置失败计数
                    else:
                        self.status["connection_health"] = "healthy"
                        consecutive_failures = 0  # 重置失败计数
                else:
                    # 如果从未收到过数据更新，检查初始化后的时间
                    if hasattr(self, '_initialization_time'):
                        time_since_init = current_time - self._initialization_time
                        if time_since_init > 30:  # 初始化后30秒还没收到数据
                            logger.warning("🚨 初始化后长时间未收到数据，尝试重连")
                            self._attempt_reconnect()

                # 监控间隔
                time.sleep(10)

            except Exception as e:
                logger.error("连接监控异常", error=str(e))
                consecutive_failures += 1

                if consecutive_failures >= max_consecutive_failures:
                    logger.error("❌ 连接监控连续失败，停止监控")
                    self.status["connection_health"] = "failed"
                    break

                time.sleep(5)

    def _restart_websocket_thread(self):
        """重启WebSocket线程"""
        try:
            logger.info("🔄 正在重启WebSocket线程...")

            # 清理现有线程
            if self.ws_thread and self.ws_thread.is_alive():
                # 等待线程结束
                self.ws_thread.join(timeout=3)

            # 重新创建WebSocket客户端
            self.ws_client = lighter.WsClient(
                order_book_ids=[self.market_id],
                account_ids=[],
                on_order_book_update=self.on_order_book_update,
                on_account_update=self.on_account_update,
            )

            # 启动新线程
            self.ws_thread = threading.Thread(target=self._run_websocket, daemon=True)
            self.ws_thread.start()

            logger.info("✅ WebSocket线程重启成功")

        except Exception as e:
            logger.error("WebSocket线程重启失败", error=str(e))

    def _attempt_reconnect(self):
        """尝试重新连接"""
        try:
            self.reconnect_attempts += 1
            self.status["reconnect_attempts"] = self.reconnect_attempts

            # 清理现有连接
            self._cleanup_connection()

            # 🔧 重置重复数据检测状态
            self.last_orderbook_hash = None
            self.duplicate_data_start_time = None

            # 等待一段时间后重连
            time.sleep(self.reconnect_interval)

            if not self.is_running:
                return

            # 重新创建WebSocket客户端
            logger.info("🔌 重新创建Lighter WebSocket连接...")
            self.ws_client = lighter.WsClient(
                order_book_ids=[self.market_id],
                account_ids=[],
                on_order_book_update=self.on_order_book_update,
                on_account_update=self.on_account_update,
            )

            # 重新启动WebSocket线程
            self.ws_thread = threading.Thread(target=self._run_websocket, daemon=True)
            self.ws_thread.start()

            logger.info(f"✅ Lighter WebSocket重连成功 (第{self.reconnect_attempts}次)")

        except Exception as e:
            logger.error(f"❌ Lighter重连失败 (第{self.reconnect_attempts}次)", error=str(e))

    def _cleanup_connection(self):
        """清理现有连接"""
        try:
            if self.ws_client:
                # 尝试关闭现有连接
                try:
                    if hasattr(self.ws_client, 'close'):
                        self.ws_client.close()
                    elif hasattr(self.ws_client, 'stop'):
                        self.ws_client.stop()
                except:
                    pass

            if self.ws_thread and self.ws_thread.is_alive():
                # 等待线程结束
                self.ws_thread.join(timeout=2)

        except Exception as e:
            logger.debug("清理连接时出现小问题", error=str(e))

    def get_orderbook(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取当前订单簿

        Args:
            symbol: 交易对符号

        Returns:
            订单簿数据，如果没有数据则抛出异常（绝不返回模拟数据）
        """
        if not self.current_orderbook:
            raise RuntimeError(f"Lighter无法获取{symbol}的真实订单簿数据 - WebSocket可能未连接")

        # 返回标准格式的订单簿数据
        orderbook_data = {
            "symbol": self.current_orderbook.symbol,
            "bids": self.current_orderbook.bids,
            "asks": self.current_orderbook.asks,
            "timestamp": self.current_orderbook.timestamp
        }

        logger.debug("📖 获取Lighter订单簿", symbol=symbol,
                    bids_count=len(self.current_orderbook.bids),
                    asks_count=len(self.current_orderbook.asks))

        return orderbook_data

    def get_current_price(self, symbol: str) -> Optional[float]:
        """
        获取当前价格

        Args:
            symbol: 交易对符号

        Returns:
            当前价格，优先返回真实成交价，否则返回买卖价中间价
        """
        # 如果有真实成交价，优先返回
        if self.current_price is not None:
            return self.current_price

        # 如果没有成交价但有订单簿数据，返回买卖价中间价
        if self.current_orderbook and self.current_orderbook.bids and self.current_orderbook.asks:
            best_bid = self.current_orderbook.bids[0][0]
            best_ask = self.current_orderbook.asks[0][0]
            mid_price = (best_bid + best_ask) / 2

            logger.debug("使用买卖价中间价作为当前价格",
                        symbol=symbol, bid=best_bid, ask=best_ask, mid_price=mid_price)
            return mid_price

        # 如果完全没有数据
        raise RuntimeError(f"Lighter无法获取{symbol}的价格数据 - WebSocket可能未连接或无订单簿数据")

    def get_latest_price_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取最新价格信息，包括数据来源说明

        Args:
            symbol: 交易对符号

        Returns:
            包含价格和数据来源的字典
        """
        try:
            if self.current_price is not None:
                return {
                    "price": self.current_price,
                    "source": "real_trade",
                    "description": "真实成交价"
                }
            elif self.current_orderbook and self.current_orderbook.bids and self.current_orderbook.asks:
                best_bid = self.current_orderbook.bids[0][0]
                best_ask = self.current_orderbook.asks[0][0]
                mid_price = (best_bid + best_ask) / 2
                return {
                    "price": mid_price,
                    "source": "orderbook_mid",
                    "description": "买卖价中间价"
                }
            else:
                return {
                    "price": None,
                    "source": "unavailable",
                    "description": "数据不可用"
                }
        except Exception as e:
            logger.error("获取价格信息失败", error=str(e))
            return {
                "price": None,
                "source": "error",
                "description": f"获取失败: {str(e)}"
            }

    def subscribe_orderbook(self, symbol: str, callback: Callable[[Dict], None]):
        """
        订阅订单簿更新

        Args:
            symbol: 交易对符号
            callback: 回调函数
        """
        self.orderbook_callbacks.append(callback)
        logger.info("📡 已订阅Lighter订单簿更新", symbol=symbol)

    def subscribe_trade(self, symbol: str, callback: Callable[[Dict], None]):
        """
        订阅交易更新

        Args:
            symbol: 交易对符号
            callback: 回调函数
        """
        self.trade_callbacks.append(callback)
        logger.info("📡 已订阅Lighter交易更新", symbol=symbol)

    def place_order(self, symbol: str, side: str, amount: float, price: Optional[float] = None, order_type: str = "limit") -> Dict[str, Any]:
        """
        下单

        Args:
            symbol: 交易对符号
            side: 买卖方向 ("buy" 或 "sell")
            amount: 数量
            price: 价格（限价单）
            order_type: 订单类型

        Returns:
            订单信息
        """
        if self.is_paper_trading:
            # 模拟交易模式
            order_id = f"lighter_paper_{int(time.time() * 1000)}"
            logger.info("📄 Lighter模拟下单",
                       symbol=symbol, side=side, amount=amount,
                       price=price, order_type=order_type, order_id=order_id)

            return {
                "order_id": order_id,
                "symbol": symbol,
                "side": side,
                "amount": amount,
                "price": price,
                "order_type": order_type,
                "status": "filled",
                "exchange": "Lighter",
                "mode": "paper_trading"
            }
        else:
            # 真实交易模式（需要实现真实的下单逻辑）
            raise NotImplementedError("Lighter真实交易下单功能待实现")

    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        current_status = self.status.copy()
        current_status["is_running"] = self.is_running
        current_status["has_orderbook"] = self.current_orderbook is not None
        current_status["has_price"] = self.current_price is not None
        return current_status

    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理Lighter客户端资源...")

        # 🔧 停止监控和重连
        self.is_running = False
        self.should_monitor = False

        # 🔧 停止连接监控线程
        if hasattr(self, 'connection_monitor_thread') and self.connection_monitor_thread and self.connection_monitor_thread.is_alive():
            logger.info("⏳ 等待连接监控线程结束...")
            self.connection_monitor_thread.join(timeout=3)
            if self.connection_monitor_thread.is_alive():
                logger.warning("连接监控线程未能在规定时间内结束")

        # 🔧 停止健康检查线程
        if hasattr(self, 'health_check_thread') and self.health_check_thread and self.health_check_thread.is_alive():
            logger.info("⏳ 等待健康检查线程结束...")
            self.health_check_thread.join(timeout=3)
            if self.health_check_thread.is_alive():
                logger.warning("健康检查线程未能在规定时间内结束")

        # 清理WebSocket连接
        self._cleanup_connection()

        # 更新状态
        self.status["connection_status"] = "disconnected"
        self.status["connection_health"] = "disconnected"
        logger.info("✅ Lighter客户端已清理")

    async def get_balance(self) -> Dict[str, Dict[str, float]]:
        """
        获取账户余额（合约交易）

        注意：Lighter的模拟交易模式返回模拟余额

        Returns:
            余额字典，与Binance客户端格式一致
        """
        try:
            if self.is_paper_trading:
                # 模拟交易模式返回模拟余额（合约交易）
                return {
                    'USDT': {'free': 10000.0, 'used': 0.0, 'total': 10000.0},  # 保证金余额
                    'free': {'USDT': 10000.0},
                    'used': {'USDT': 0.0},
                    'total': {'USDT': 10000.0}
                }
            else:
                # 真实交易需要调用Lighter API获取余额
                # 这里先返回空字典，因为我们目前只支持订单簿数据
                logger.warning("Lighter真实交易余额查询功能尚未实现")
                return {
                    'USDT': {'free': 0.0, 'used': 0.0, 'total': 0.0},
                    'free': {'USDT': 0.0},
                    'used': {'USDT': 0.0},
                    'total': {'USDT': 0.0}
                }
        except Exception as e:
            logger.error("获取Lighter余额失败", error=str(e))
            return {
                'USDT': {'free': 0.0, 'used': 0.0, 'total': 0.0},
                'free': {'USDT': 0.0},
                'used': {'USDT': 0.0},
                'total': {'USDT': 0.0}
            }

    async def get_positions(self) -> Dict[str, Dict[str, float]]:
        """
        获取合约持仓信息

        Returns:
            持仓信息
        """
        try:
            if self.is_paper_trading:
                # 模拟交易模式下返回空持仓（项目启动时持仓为0）
                return {
                    'BTC/USDT': {
                        'size': 0.0,  # 持仓数量
                        'side': 'none',  # 持仓方向: long/short/none
                        'unrealized_pnl': 0.0,  # 未实现盈亏
                        'entry_price': 0.0,  # 开仓价格
                        'mark_price': 0.0,  # 标记价格
                        'margin': 0.0  # 保证金
                    }
                }
            else:
                # 真实交易需要调用Lighter API获取持仓
                logger.warning("Lighter真实交易持仓查询功能尚未实现")
                return {}

        except Exception as e:
            logger.error("获取Lighter持仓失败", error=str(e))
            return {}

    async def close(self) -> None:
        """
        关闭客户端连接
        """
        try:
            logger.info("🛑 正在关闭Lighter客户端...")

            # 🔧 停止监控和重连
            self.is_running = False
            self.should_monitor = False

            # 🔧 停止连接监控线程
            if hasattr(self, 'connection_monitor_thread') and self.connection_monitor_thread and self.connection_monitor_thread.is_alive():
                logger.info("⏳ 等待连接监控线程结束...")
                self.connection_monitor_thread.join(timeout=3)
                if self.connection_monitor_thread.is_alive():
                    logger.warning("连接监控线程未能在规定时间内结束")

            # 🔧 停止健康检查线程
            if hasattr(self, 'health_check_thread') and self.health_check_thread and self.health_check_thread.is_alive():
                logger.info("⏳ 等待健康检查线程结束...")
                self.health_check_thread.join(timeout=3)
                if self.health_check_thread.is_alive():
                    logger.warning("健康检查线程未能在规定时间内结束")

            # 清理WebSocket客户端
            if hasattr(self, 'ws_client') and self.ws_client:
                logger.info("🔌 正在关闭Lighter WebSocket连接...")
                try:
                    # 如果WebSocket客户端有close方法，尝试调用
                    if hasattr(self.ws_client, 'close'):
                        await self.ws_client.close()
                    elif hasattr(self.ws_client, 'stop'):
                        self.ws_client.stop()
                except Exception as e:
                    logger.debug("WebSocket客户端关闭时的小问题", error=str(e))
                logger.info("✅ Lighter WebSocket连接已关闭")

            # 等待WebSocket线程结束
            if hasattr(self, 'ws_thread') and self.ws_thread and self.ws_thread.is_alive():
                logger.info("⏳ 等待WebSocket线程结束...")
                self.ws_thread.join(timeout=3)
                if self.ws_thread.is_alive():
                    logger.warning("WebSocket线程未能在规定时间内结束")

            # 清理数据
            self.current_orderbook = None
            self.current_price = None
            self.last_update_time = None
            if hasattr(self, 'orderbook_callbacks'):
                self.orderbook_callbacks.clear()
            if hasattr(self, 'trade_callbacks'):
                self.trade_callbacks.clear()

            # 更新状态
            self.status["connection_status"] = "closed"
            self.status["connection_health"] = "closed"

            logger.info("🛑 Lighter客户端已完全关闭")

        except Exception as e:
            logger.error("关闭Lighter客户端失败", error=str(e))